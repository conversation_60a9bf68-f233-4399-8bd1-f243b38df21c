# Generated by Django 5.2.5 on 2025-08-23 22:58

import django.db.models.deletion
import django.utils.timezone
import sl_app.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sl_app', '0007_alter_userprofile_profile_picture'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Loan',
            fields=[
                ('loan_id', models.AutoField(primary_key=True, serialize=False)),
                ('borrowed_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('due_date', models.DateTimeField(default=sl_app.models.default_due_date)),
                ('returned_at', models.DateTimeField(blank=True, null=True)),
                ('fine_amount', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='sl_app.book')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
