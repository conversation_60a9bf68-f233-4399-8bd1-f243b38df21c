{% extends 'main/staff_base.html' %}
{% block title %}Manage Books{% endblock %}

{% block content %}
<div class="container mt-4 center" style="width:1600px;">
    <div class="d-flex justify-content-between align-items-center mb-3" style="width:1600px;">
        <h1 class="fw-bold sl-text-primary">📚 Manage Books</h1>
        <a href="{% url 'book_create' %}" class="btn btn-success">
            <i class="bi bi-plus-circle"></i> Add New Book
        </a>
    </div>
<div class="mb-3" style="width:1600px;">
<form method="get" class="row g-2 align-items-end">
    <div class="col-md-3">
        <input type="text" name="title" class="form-control" placeholder="Search by Title" value="{{ request.GET.title }}">
    </div>
    <div class="col-md-3">
        <input type="text" name="author" class="form-control" placeholder="Search by Author" value="{{ request.GET.author }}">
    </div>
    <div class="col-md-3">
        <input type="text" name="genre" class="form-control" placeholder="Search by Genre" value="{{ request.GET.genre }}">
    </div>
    <div class="col-md-3 d-flex gap-2">
        <button type="submit" class="btn btn-primary flex-grow-1">
            <i class="bi bi-search"></i> Search
        </button>
        <button type="button" class="btn btn-secondary flex-grow-1" onclick="window.location.href='{{ request.path }}'">
            <i class="bi bi-x-circle"></i> Clear
        </button>
    </div>
</form>

</div>
    <div class="card shadow rounded-3 " style="width:1600px;">
        <div class="card-body ">
            <div class="table-responsive ">
                <table class="table table-hover align-middle ">
                    <thead class="table-dark">
                        <tr>
                            <th>Title</th>
                            <th>Author</th>
                            <th>Genre</th>
                            <th>Published Year</th>
                            <th>ISBN</th>
                        <th>Total Copies</th>
                        <th>Copies Available</th>
                        <th>Cover</th>
                        <th>Availability</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for book in page_obj %}
                        <tr>
                            <td><a href="{% url 'book_detail' book.book_id %}">{{ book.title }} </a></td>
                            <td>{{ book.author }}</td>
                            <td>{{ book.genre }}</td>
                            <td>{{ book.publication_year }}</td>
                            <td>{{ book.isbn }}</td>
                        <td>{{ book.total_copies }}</td>
                        <td>{{ book.copies_available }}</td>
                            <td>
                                {% if book.cover_image %}
                                    <img src="{{ book.cover_image.url }}" alt="Cover" class="img-thumbnail" style="max-width:60px;">
                                {% else %}
                                    <span class="text-muted">No Cover</span>
                                {% endif %}
                            </td>
                            <td>
                            {% if book.availability %}
                                <span class="badge bg-success">Available</span>
                            {% else %}
                                <span class="badge bg-danger">Not Available</span>
                            {% endif %}
                        </td>
                            <td class="text-center">
                                <a href="{% url 'book_update' book.pk %}" class="btn btn-sm btn-primary">
                                    <i class="bi bi-pencil-square"></i> Edit
                                </a>
                                <form action="{% url 'book_delete' book.pk %}" method="post" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this book?');">
                                        <i class="bi bi-trash"></i> Delete
                                    </button>
                                </form>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center text-muted">No books found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination Controls -->
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-3">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Previous</span></li>
                    {% endif %}

                    <li class="page-item disabled">
                        <span class="page-link">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Next</span></li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
</div>
{% endblock %}
