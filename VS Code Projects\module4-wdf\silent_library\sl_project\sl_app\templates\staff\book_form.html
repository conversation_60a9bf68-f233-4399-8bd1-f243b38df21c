{% extends 'main/staff_base.html' %}
{% block title %}Manage Books{% endblock %}

{% block content %}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">

            <div class="card shadow rounded-3 mx-auto p-3">
                <div class="card-header custom-header text-white">
                    <h4 class="mb-0">{% if book %}Edit{% else %}Add{% endif %} Book</h4>
                </div>
               <div class="card-body p-4 bg-light">  <!-- Light background + padding -->
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="row">
                            <!-- Left column -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Title</label>
                                    <input type="text" name="title" class="form-control" value="{{ book.title|default:'' }}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Author</label>
                             <select name="author" class="form-control">
                                <option value="" selected disabled>-- Select Author --</option>
  {% for a in authors %}
    <option value="{{ a.author_id }}" {% if book.author == a %}selected{% endif %}>{{ a.author_name }}</option>
  {% endfor %}
</select>               </div>

                                <div class="mb-3">
                                    <label class="form-label">Genre</label>
                          <select name="genre" class="form-control">
                            <option value="" selected disabled>-- Select Genre --</option>
  {% for a in genres %}
    <option value="{{ a.genre_id }}" {% if book.genre == a %}selected{% endif %}>{{ a.genre_name }}</option>
  {% endfor %}
</select>           </div>
                                 <div class="mb-3">
                                    <label class="form-label">ISBN</label>
                                    <input type="text" name="isbn" class="form-control" value="{{ book.isbn|default:'' }}" required>
                                </div>
                                 <div class="mb-3">
                                      <label for="availability">Book Availability Status</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
  <input type="checkbox" id="availability" name="availability" value="True"
         {% if book.availability %}checked{% endif %}>
 
                                </div>
<div class="mb-3">
                                    <label class="form-label">Total Copies</label>
                                    <input type="text" name="total_copies" class="form-control" value="{{ book.total_copies|default:'' }}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Copies Available</label>
                                    <input type="text" name="copies_available" class="form-control" value="{{ book.copies_available|default:'' }}" required>
                                </div>

                            </div>

                            <!-- Right column -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Publication Year</label>
                                    <input type="text" name="publication_year" class="form-control" value="{{ book.publication_year|default:'' }}" required>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Book Description</label>
                           <div class="form-group">
 
  <textarea id="description" name="description" class="form-control" rows="4" required>{{ book.description|default:'' }}</textarea>
</div>     </div>

                                <div class="mb-3">
                                    <label class="form-label">Cover Image {% if book.cover_image %}
                                        ({{ book.cover_image.url }})
                                    {% endif %} </label>
                                    <input type="file" name="cover_image" class="form-control">
                                    {% if book.cover_image %}
                                        <div class="mt-2">
                                            <img src="{{ book.cover_image.url }}" width="120" class="img-thumbnail">
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 text-center">
                            <button type="submit" class="btn btn-success">Save</button>
                            <a href="javascript:history.back()" class="btn btn-secondary ms-2">Back</a>

                        </div>
                    </form>
                </div>
            </div>

        </div>
    </div>
</div>

{% endblock %}
