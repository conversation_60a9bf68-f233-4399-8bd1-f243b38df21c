from django.urls import reverse
from .models import *
from django.test import TestCase
from django.contrib.messages import get_messages
from django.contrib.staticfiles.testing import StaticLiveServerTestCase
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC


class BookModelTest(TestCase):

    def setUp(self):
        # Create test Author and Genre
        self.author = Author.objects.create(author_name="Test Author")
        self.genre = Genre.objects.create(genre_name="Test Genre")

        # Create a Book instance
        self.book = Book.objects.create(
            title="Test Book",
            author=self.author,
            publication_year=2020,
            isbn="1234567890",
            description="This is a test book.",
            total_copies=10,
            copies_available=5,
            genre=self.genre,
            availability=True
        )

    def test_create_book(self):
        """Test that a Book is created correctly"""
        book_count = Book.objects.count()
        self.assertEqual(book_count, 1)
        self.assertEqual(self.book.title, "Test Book")
        self.assertEqual(self.book.copies_available, 5)
        self.assertTrue(self.book.availability)

    def test_read_book(self):
        """Test fetching a Book from database"""
        book = Book.objects.get(pk=self.book.book_id)
        self.assertEqual(book.author.author_name, "Test Author")
        self.assertEqual(book.genre.genre_name, "Test Genre")

    def test_update_book(self):
        """Test updating Book fields"""
        self.book.copies_available = 8
        self.book.title = "Updated Book"
        self.book.save()

        updated_book = Book.objects.get(pk=self.book.book_id)
        self.assertEqual(updated_book.copies_available, 8)
        self.assertEqual(updated_book.title, "Updated Book")

    def test_delete_book(self):
        """Test deleting a Book"""
        self.book.delete()
        book_count = Book.objects.count()
        self.assertEqual(book_count, 0)
class SignUpViewTest(TestCase):
    
    def test_signup_success(self):
        response = self.client.post(reverse("signup"), {
            "username": "viewtest",
            "first_name": "View",
            "last_name": "Test",
            "gender": "M",
            "bio": "This is a test bio",
            "email": "<EMAIL>",
            "password": "mypassword123",
            "confirm_password": "mypassword123"
        })
        self.assertEqual(response.status_code, 302)  # Redirects on success
        self.assertTrue(User.objects.filter(username="viewtest").exists())
    
    
    # Test for password mismatch and missing first_name and last name and gender
    def test_signup_failure(self):
        response = self.client.post(reverse("signup"), {
            "username": "viewtest",
            "email": "<EMAIL>",
            "password": "mypassword123",
            "confirm_password": "wrongpassword"
        })
        self.assertEqual(response.status_code, 200)  # stays on signup page
        self.assertFalse(User.objects.filter(username="viewtest").exists())
        # Capture messages
        messages = list(get_messages(response.wsgi_request))
        message_texts = [str(m) for m in messages]

        # Assert your custom error messages
        self.assertIn("First Name and Last Name are required.", message_texts)
        self.assertIn("Please select a valid gender.", message_texts)
        self.assertIn("Passwords do not match.", message_texts)
        


class BorrowBookIntegrationTest(TestCase):

    def setUp(self):
        # Create a user
        self.user = User.objects.create_user(
            username='testuser', password='testpass123'
        )
        # Create related objects first
        self.author = Author.objects.create(author_name='Test Author')
        self.genre = Genre.objects.create(genre_name='Fiction')
        # Create a book
        self.book = Book.objects.create(
            title='Test Book',
            author=self.author,
            genre=self.genre,
            availability=True,
            publication_year=2001,
            isbn='1234567890',
            description='Test Description',
            total_copies=1,
            copies_available=1,
        )
        
        

    def test_borrow_book_success(self):
        # Log in the user
        self.client.login(username='testuser', password='testpass123')

        # Submit borrow request
        response = self.client.post(reverse('borrow_book', args=[self.book.book_id]))

        # Check redirect (or success page)
        self.assertEqual(response.status_code, 302)
         # Capture messages
        messages = list(get_messages(response.wsgi_request))
        message_texts = [str(m) for m in messages]

        # Assert your custom error messages
        self.assertIn(f"Book '{self.book.title}' has been borrowed successfully!", message_texts)
        

        # Check loan record created
        loan = Loan.objects.filter(user=self.user, book=self.book).first()
        self.assertIsNotNone(loan)

        # Check book availability updated
        self.book.refresh_from_db()
        self.assertFalse(self.book.availability)

    def test_borrow_book_already_borrowed(self):
        # Borrow once
        self.book.availability = False
        self.book.save()

        self.client.login(username='testuser', password='testpass123')
        response = self.client.post(reverse('borrow_book', args=[self.book.book_id]))

        # Should not create a loan
        self.assertFalse(Loan.objects.filter(user=self.user, book=self.book).exists())
       
        messages = list(get_messages(response.wsgi_request))
        self.assertIn( f"Book '{self.book.title}' is not available for borrowing.", [str(m) for m in messages])
        
class SignUpSystemTest(StaticLiveServerTestCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.browser = webdriver.Chrome()  # or Firefox
        
        
    def setUp(self):
        # Create related objects first
        self.author = Author.objects.create(author_name='Test Author')
        self.genre = Genre.objects.create(genre_name='Fiction')
        # Create a book
        self.book = Book.objects.create(
            title='Test Book',
            author=self.author,
            genre=self.genre,
            availability=True,
            publication_year=2001,
            isbn='1234567890',
            description='Test Description',
            total_copies=1,
            copies_available=1,
        )
        
    @classmethod
    def tearDownClass(cls):
        cls.browser.quit()
        super().tearDownClass()

    def test_user_signup_flow(self):
        self.browser.get(f'{self.live_server_url}/signup/')

        # Fill form
        self.browser.find_element(By.NAME, 'username').send_keys('sysuser')
        self.browser.find_element(By.NAME, 'email').send_keys('<EMAIL>')
        self.browser.find_element(By.NAME, 'first_name').send_keys('John')
        self.browser.find_element(By.NAME, 'last_name').send_keys('Doe')
        self.browser.find_element(By.NAME, 'password').send_keys('password123')
        self.browser.find_element(By.NAME, 'gender').send_keys('M')
        self.browser.find_element(By.NAME, 'confirm_password').send_keys('password123')

        # Try with different waiting strategies
        import time
        time.sleep(4)  # Temporary fix for timing issues
        self.browser.find_element(By.XPATH, '//button[text()="Sign Up"]').click()
                
        print ("Signup button clicked")

        # Check redirected to login page
        self.assertIn('/signup_success/', self.browser.current_url)
        
        print  ("Signup success page found")
        time.sleep(7) 
        
        # ---- LOGIN ----
        self.browser.get(f'{self.live_server_url}/login/')
        self.browser.find_element(By.NAME, 'username').send_keys('sysuser')
        self.browser.find_element(By.NAME, 'password').send_keys('password123')
        time.sleep(4)
        self.browser.find_element(By.XPATH, '//button[text()="Login"]').click() 
        
        print ("Login button clicked")
        time.sleep(4)
        
        # ---- Browse BOOK ----
        self.browser.get(f'{self.live_server_url}/browse_books/')
        
        print ("Browse Books page loaded")
        time.sleep(4)
        self.assertIn('Browse Books by Category', self.browser.page_source)
        print ("Browse Books by Category message found")
        # Borrow first book
        
        borrow_link = WebDriverWait(self.browser, 30).until(
            EC.element_to_be_clickable(
                (By.XPATH, '(//a[contains(normalize-space(text()), "Borrow")])[1]')
            )
        )

        # Scroll into view (optional, helps avoid interception)
        self.browser.execute_script("arguments[0].scrollIntoView(true);", borrow_link)
        self.browser.execute_script("arguments[0].click();", borrow_link)
       

        print ("Borrow button clicked")
        time.sleep(10)
        
        #---Borrow Book        
        self.assertIn('/book/', self.browser.current_url)
        print ("Book detail page loaded")
        
        
        # Find the borrow link
        borrow_link = WebDriverWait(self.browser, 30).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, "a.btn-success"))
        )

        # Scroll into view and click (triggers the JS confirm)
        self.browser.execute_script("arguments[0].scrollIntoView(true);", borrow_link)
        self.browser.execute_script("arguments[0].click();", borrow_link)
       

        # Immediately handle the JS confirm
        alert = WebDriverWait(self.browser, 5).until(EC.alert_is_present())
        alert.accept()  # click
        
        time.sleep(4)

        # Check borrow success (depends on your UI)
        self.assertIn('has been borrowed successfully!', self.browser.page_source)
        print ("Borrow success message found")
       