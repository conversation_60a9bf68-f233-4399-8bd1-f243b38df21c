from django.contrib import admin
from .models import *
from django.utils.html import format_html

class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'profile_picture_tag', 'bio')
    readonly_fields = ('profile_picture_tag',)  # So it shows in edit page

    def profile_picture_tag(self, obj):
        if obj.profile_picture:
            return format_html('<img src="{}" width="100" height="100" style="object-fit: cover;" />', obj.profile_picture.url)
        return "(No image)"

    profile_picture_tag.short_description = 'Profile Picture'



# Simple registration (basic)
admin.site.register(Author)
admin.site.register(Genre)
admin.site.register(Book)
admin.site.register(UserProfile, UserProfileAdmin)
