from django.contrib import admin
from django.urls import path, include
from . import views
from django.contrib.auth import views as auth_views
from .views import CustomLoginView

urlpatterns = [
    # Public Pages
    path('', views.landing_page, name="landing_page"),
    path('home/', views.home_page, name="home_page"),
    path('contact/', views.contact_page, name="contact_page"),
    path('privacy/', views.privacy_page, name="privacy_page"),
    path('sitemap/', views.sitemap_page, name="sitemap_page"),
    path('events/', views.events_page, name="events_page"),

    # Authentication & User Management
    path('login/', CustomLoginView.as_view(), name='login'),
    path('logout/', views.logout_page, name='logout'),
    path("signup/", views.signup_page, name="signup"),
    path("signup_success/", views.signup_success_page, name="signup_success"),
    path('change_password/', views.change_password, name='change_password'),

    # Password Reset
    path("password_reset/", views.password_reset_page, name="password_reset"),
    path('password_reset_verify/', views.password_reset_verify, name='password_reset_verify'),
    path('password_reset_confirm/', views.password_reset_confirm, name='password_reset_confirm'),

    # User Profile
    path('profile/', views.profile_view, name='profile'),
    path('profile/edit/', views.profile_edit, name='edit_profile'),
    
    # Books & Library Features
    path('books/', views.books_page, name="books_page"),
    path('book_search/', views.book_search_page, name='book_search'),
    path('advanced_search/', views.advanced_search_page, name="advanced_search"),
    path('browse_books/', views.browse_books, name='browse_books'),
    path('return_book/<int:loan_id>/', views.return_book, name='return_book'),
    path('book/<int:book_id>/review/', views.review_page, name='add_review'),
    path("book/<int:book_id>/borrow/", views.borrow_book, name="borrow_book"),
    #to be created
    path("genre/<int:genre_id>/", views.browse_books, name="genre_books"),
    path('return_book/<int:loan_id>/', views.return_book, name='return_book'),
    path("categories/", views.browse_books, name="all_categories"),
        
    # Staff Management
    path("staff/", views.staff_dashboard, name="staff"),
    path("staff_dashboard/", views.staff_dashboard, name="staff_dashboard"),
    path('book_list/', views.book_list, name='book_list'),
    path('book_create/', views.book_create, name='book_create'),
    path('book/<int:book_id>/', views.book_detail_page, name='book_detail'),
    path('book_update/<int:pk>/', views.book_update, name='book_update'),
    path('book_delete/<int:pk>/', views.book_delete, name='book_delete'), 
    path('user_list', views.user_list, name='user_list'),
    path('user_create', views.user_create, name='user_create'),
    path('user_edit/<int:user_id>/edit/', views.user_edit, name='user_edit'),
    path('user_delete/<int:user_id>/delete/', views.user_delete, name='user_delete'),
    path('user_activate/<int:user_id>/activate/', views.user_activate, name='user_activate'),
    path('user_deactivate/<int:user_id>/deactivate/', views.user_deactivate, name='user_deactivate'),
      
    # Individual Book Details (Legacy)
    path('book_details_1/', views.book_details_1_page, name="book_details_1"),
    path('book_details_2/', views.book_details_2_page, name="book_details_2"),
    path('book_details_3/', views.book_details_3_page, name="book_details_3"),
]
   