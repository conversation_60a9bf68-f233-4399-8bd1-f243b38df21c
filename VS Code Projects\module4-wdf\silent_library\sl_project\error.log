Internal Server Error: /book_search/
Traceback (most recent call last):
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\sl_project\sl_app\views.py", line 712, in book_search_page
    books = Book.objects.all.order_by('title')
            ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'function' object has no attribute 'order_by'
"GET /book_search/ HTTP/1.1" 500 74129
Internal Server Error: /books/
Traceback (most recent call last):
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\sl_project\sl_app\views.py", line 923, in books_page
    return render(request, "books.html")
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: books.html
"GET /books/ HTTP/1.1" 500 89317
Internal Server Error: /change_password/
Traceback (most recent call last):
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\sl_project\sl_app\views.py", line 613, in change_password
    return render(request, 'change_password.html', {'form': form, 'base_template': base_template})
                                                                                   ^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'base_template' where it is not associated with a value
"GET /change_password/ HTTP/1.1" 500 79892
Internal Server Error: /user_list
Traceback (most recent call last):
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\sl_project\sl_app\views.py", line 262, in user_list
    return render(request, 'staff/user_list.html', {
        'users': users,
    })
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'user_deactivate' not found. 'user_deactivate' is not a valid view function or pattern name.
"GET /user_list HTTP/1.1" 500 199197
Internal Server Error: /book/1110/
Traceback (most recent call last):
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
TypeError: int() argument must be a string, a bytes-like object or a real number, not 'SimpleLazyObject'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\sl_project\sl_app\views.py", line 759, in book_detail_page
    user_review = Review.objects.filter(book=book, user=request.user).first()
                  ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\query.py", line 1493, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\query.py", line 1511, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\query.py", line 1518, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\sql\query.py", line 1646, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\sql\query.py", line 1678, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\sql\query.py", line 1588, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\sql\query.py", line 1415, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
TypeError: Field 'id' expected a number but got <SimpleLazyObject: <django.contrib.auth.models.AnonymousUser object at 0x000001B783AA0710>>.
"GET /book/1110/ HTTP/1.1" 500 140689
Internal Server Error: /book/1110/
Traceback (most recent call last):
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\sl_project\sl_app\views.py", line 785, in book_detail_page
    'review': user_review,
              ^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'user_review' where it is not associated with a value
"GET /book/1110/ HTTP/1.1" 500 76152
Internal Server Error: /book/1110/
Traceback (most recent call last):
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\sl_project\sl_app\views.py", line 751, in book_detail_page
    user_review = None
    ^^^^
NameError: name 'user' is not defined. Did you mean: 'User'?
"GET /book/1110/ HTTP/1.1" 500 74212
Internal Server Error: /books/
Traceback (most recent call last):
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\sl_project\sl_app\views.py", line 900, in books_page
    return render(request, "books.html")
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "D:\Personal\Lithan\VS Code Projects\module4-wdf\silent_library\.sl\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: books.html
