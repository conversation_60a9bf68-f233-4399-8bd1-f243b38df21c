{% extends base_template %}
{% load static %}

{% block title %}Change Password{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header custom-header text-white">
                    <h4 class="mb-0">Change Password</h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        {{ form.old_password.label_tag }} 
                        {{ form.old_password }}
                        <br>
                        {{ form.new_password1.label_tag }}
                        {{ form.new_password1 }}
                        <br>
                        {{ form.new_password2.label_tag }}
                        {{ form.new_password2 }}
                        
                        <div class="mt-3 text-center">
                        <button type="submit" class="btn btn-success ms-2">Update Password</button>
                        <a href="{% url 'profile' %}" class="btn btn-secondary ms-2">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
