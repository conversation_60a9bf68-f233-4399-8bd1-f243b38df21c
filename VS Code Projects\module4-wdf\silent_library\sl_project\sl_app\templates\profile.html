{% extends base_template %}
{% load static %}
{% block title %}User Profile{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-md-4 text-center">
      {% if profile.profile_picture %}
        <img src="{{ profile.profile_picture.url }}" class="profile-picture mb-3 img-fluid rounded-circle" alt="Profile Picture">
      {% else %}
        {% if profile.gender == 'M' %}
          <img src="/media/profile_pictures/avatar_male.jpg" alt="Male Avatar" class="profile-picture mb-3 img-fluid rounded-circle">
        {% else %}
          <img src="/media/profile_pictures/avatar_female.jpg" alt="Female Avatar" class="profile-picture mb-3 img-fluid rounded-circle">
        {% endif %}
      {% endif %}
      <h3>{{ user.get_full_name|default:user.username }}</h3>
      <p class="text-muted">@{{ user.username }}</p>
    </div>

    <div class="col-md-8">
      {% if edit_mode %}
        <form method="POST" enctype="multipart/form-data">
          {% csrf_token %}
          <ul class="list-group list-group-flush">
            <li class="list-group-item"><strong>First Name:</strong> {{ form.first_name }}</li>
            <li class="list-group-item"><strong>Last Name:</strong> {{ form.last_name }}</li>
            <li class="list-group-item"><strong>Email:</strong> {{ form.email }}</li>
            <li class="list-group-item"><strong>Bio:</strong> {{ form.bio }}</li>
            <li class="list-group-item"><strong>Profile Picture:</strong> {{ form.profile_picture }}</li>
            <li class="list-group-item"><strong>Gender:</strong> {{ form.gender }}</li>
          </ul>
          <div class="mt-4 text-center">
            <button type="submit" class="btn btn-primary">Save Changes</button>
            <a href="{% url 'change_password' %}" class="btn btn-outline-primary">Change Password</a>
            <a href="{% url 'profile' %}" class="btn btn-secondary">Cancel</a>
          </div>
        </form>
      {% else %}
        <ul class="list-group list-group-flush">
          <li class="list-group-item"><strong>First Name:</strong> {{ user.first_name }}</li>
          <li class="list-group-item"><strong>Last Name:</strong> {{ user.last_name }}</li>
          <li class="list-group-item"><strong>Email:</strong> {{ user.email }}</li>
          <li class="list-group-item"><strong>Bio:</strong> {{ profile.bio }}</li>
          <li class="list-group-item"><strong>Gender:</strong> {{ profile.gender }}</li>
        </ul>
        <div class="mt-4 text-center">
          <a href="{% url 'edit_profile' %}" class="btn btn-primary">Edit Profile</a>
          <a href="{% url 'change_password' %}" class="btn btn-outline-primary">Change Password</a>
          <a href="{% url 'logout' %}" class="btn btn-outline-danger">Logout</a>
        </div>
      {% endif %}
    </div>
  </div>
</div>


  {% if not user.is_staff and loans %}
  <div class="loan-history mt-5  w-50 mx-auto text-center">
    <h4>Loan History</h4>
    <table class="table table-bordered mt-3">
      <thead>
        <tr>
          <th>Book Title</th>
          <th>Borrowed At</th>
          <th>Due Date</th>
          <th>Returned At</th>
          <th>Fine Amount</th>
          <th>Review</th>
        </tr>
      </thead>
      <tbody>
        {% for loan in loans %}
        <tr>
          <td><a href="{% url 'book_detail' loan.book.book_id %}"> {{ loan.book.title }} </a></td>
          <td>{{ loan.borrowed_at|date:"Y-m-d H:i" }}</td>
          <td>{{ loan.due_date|date:"Y-m-d H:i" }}</td>
          <td>
            {% if loan.returned_at %}
              {{ loan.returned_at|date:"Y-m-d H:i" }}
            {% else %}
              <a href="{% url 'return_book' loan.loan_id %}" class="btn btn-warning" onclick="return confirm('Are you sure you want to return this book?');">Return</a>
            {% endif %}
          </td>
          <td>{{ loan.fine_amount }}</td>
          <td>           

{% if loan.user_review %}
      <!-- Button to trigger modal -->
      <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#reviewModal{{ loan.user_review.review_id }}">
        View Review
      </button>
      <!-- Modal -->
      <div class="modal fade" id="reviewModal{{ loan.user_review.review_id }}" tabindex="-1" aria-labelledby="reviewModalLabel{{ loan.user_review.review_id }}" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="reviewModalLabel{{ loan.user_review.review_id }}">
                Your Review for "{{ loan.book.title }}"
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <p><strong>Rating:</strong> {{ loan.user_review.rating }} ⭐</p>
              <p><strong>Comment:</strong></p>
              <p>{{ loan.user_review.comment|default:"No comment" }}</p>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    {% else %}
      <a href="{% url 'add_review' loan.book.book_id %}" class="btn btn-primary">Write Review</a>
    {% endif %}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% endif %}
</div>
{% endblock %}
