{% extends base_template %}
{% load static %}
{% block title %}{{ book.title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>{{ book.title }}</h2>
    <div class="row mt-3">
        <div class="col-md-4">
            {% if book.cover_image %}
                <img src="{{ book.cover_image.url }}" alt="{{ book.title }}" class="img-fluid">
            {% else %}
                <img src="{% static 'images/default-cover.png' %}" alt="No Cover" class="img-fluid">
            {% endif %}
        </div>
        <div class="col-md-8">
            <table class="table table-bordered">
                <tr><th>Title</th><td>{{ book.title }}</td></tr>
                <tr><th>Author</th><td>{{ book.author.author_name }}</td></tr>
                <tr><th>Genre</th><td>{{ book.genre.genre_name }}</td></tr>
                <tr><th>Publication Year</th><td>{{ book.publication_year }}</td></tr>
                <tr><th>ISBN</th><td>{{ book.isbn }}</td></tr>
                <tr><th>Total Copies</th><td>{{ book.total_copies }}</td></tr>
                <tr><th>Copies Available</th><td>{{ book.copies_available }}</td></tr>
                <tr><th>Availability</th><td>{{ book.availability|yesno:"Yes,No" }}</td></tr>
                <tr><th>Description</th><td>{{ book.description }}</td></tr>
            </table>

            {% if request.user.is_authenticated %}
                {% if request.user.is_staff %}
                    <a href="{% url 'book_update' book.book_id %}" class="btn btn-warning ms-2">Edit</a>
                {% else %}
                    {% if loan %}
                        <p><strong>Borrowed Date:</strong> {{ loan.borrowed_at|date:"M d, Y" }}</p>
                        <p><strong>Due Date:</strong> {{ loan.due_date|date:"M d, Y" }}</p>
                        <a href="{% url 'return_book' loan.loan_id %}" class="btn btn-warning ms-2"
                           onclick="return confirm('Are you sure you want to return this book?');">
                           Return
                        </a>
                    {% else %}
                        {% if book.copies_available > 0 %}
                            <a href="{% url 'borrow_book' book.book_id %}" class="btn btn-success ms-2"
                               onclick="return confirm('Are you sure you want to borrow this book?');">
                               Borrow
                            </a>
                        {% else %}
                            <button class="btn btn-secondary ms-2" disabled>Not Available</button>
                        {% endif %}
                    {% endif %}

                   
                        {% if review %}
                            <!-- View Review Button -->
                            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#reviewModal{{ review.review_id }}">
                                View Review
                            </button>

                            <!-- Review Modal -->
                            <div class="modal fade" id="reviewModal{{ review.review_id }}" tabindex="-1" aria-labelledby="reviewModalLabel{{ review.review_id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="reviewModalLabel{{ review.review_id }}">
                                                Your Review for "{{ loan.book.title }}"
                                            </h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p><strong>Rating:</strong> {{ review.rating }} ⭐</p>
                                            <p><strong>Comment:</strong></p>
                                            <p>{{ review.comment|default:"No comment" }}</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <a href="{% url 'add_review' book.book_id %}" class="btn btn-primary ms-2">Write Review</a>
                        {% endif %}
                    
                {% endif %}
            {% endif %}

            <a href="javascript:history.back()" class="btn btn-secondary ms-2">Back</a>
        </div>
    </div>
</div>



<div class="container mt-3" style="max-width: 1000px;">
    <hr>
<h4>Reviews</h4>
    {% if reviews_page %}
        {% for rev in reviews_page %}
            <div class="card mb-3">
                <div class="card-body">
                    <h6 class="card-subtitle mb-2 text-muted">
    {{ rev.masked_username }}
    <span class="float-end">{{ rev.created_at|date:"M d, Y H:i" }}</span>
</h6>
                    <p class="card-text">
                        <strong>Rating:</strong> {{ rev.rating }} ⭐<br>
                        <strong>Comment:</strong> {{ rev.comment|default:"No comment" }}
                    </p>
                </div>
            </div>
        {% endfor %}

        {% if reviews_page.has_other_pages %}
            <nav aria-label="Review pagination">
                <ul class="pagination justify-content-center">
                    {% if reviews_page.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ reviews_page.previous_page_number }}">Previous</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Previous</span></li>
                    {% endif %}

                    {% for num in reviews_page.paginator.page_range %}
                        {% if reviews_page.number == num %}
                            <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                        {% else %}
                            <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                        {% endif %}
                    {% endfor %}

                    {% if reviews_page.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ reviews_page.next_page_number }}">Next</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Next</span></li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <p>No reviews yet.</p>
    {% endif %}
</div>

{% endblock %}
