/* ===========================
  Base Styles
  =========================== */
body {
  font-family: 'Open Sans', sans-serif;
  font-size: 16px;
}

html,
body {
  margin: 0;
  padding: 0;
}

/* ===========================
  Typography
  =========================== */
h1,
h2,
h3 {
  font-family: 'Merriweather', serif;
  font-weight: bold;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 24px;
}

h3 {
  font-size: 20px;
}

section h2 {
  margin-top: 0 !important;
}

.caption {
  font-size: 14px;
}

/* ===========================
  Links & Buttons
  =========================== */
a {
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #ffc107;
}

.btn-primary {
  background-color: #1D3557;
  border: none;
}

.btn-primary:hover {
  background-color: #16324F;
}

.cta-button {
  background-color: #F1FAEE;
  color: #1D3557;
  border: none;
  padding: 15px 30px;
  font-size: 1em;
  font-weight: 500;
  border-radius: 24px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cta-button:hover {
  background-color: #e6f0ec;
}

/* ===========================
  Carousel Styles
  =========================== */
.carousel-item {
  background-color: #d1dff2;
  color: white;
  padding: 10px 0;
}

.carousel-item .btn {
  background-color: #1D3557;
  color: #ffffff;
}

.carousel-item .btn:hover {
  background-color: #7ca2d8;
}

/* Carousel Prev/Next Buttons */
.carousel-control-prev,
.carousel-control-next {
  width: 5%;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  filter: invert(0%) brightness(0%) contrast(200%);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  width: 4rem;
  height: 4rem;
  background-size: 100% 100%;
}

@media (max-width: 991.98px) {

  .carousel-control-prev,
  .carousel-control-next {
    top: auto;
    bottom: 10px;
    transform: none;
  }
}

.carousel-control-prev {
  left: 30px;
}

.carousel-control-next {
  right: 30px;
}


/* Base style: hidden by default */
.fade-arrow {
  opacity: 0;
  transition: opacity 0.1s ease;
}

/* Show arrows only when hovering over carousel */
#silentLibraryCarousel:hover .fade-arrow {
  opacity: 1;
}

/* Optional: shrink width for tighter control position */
.carousel-nav {
  width: 4%;
}


/* ===========================
  Banner Styles
  =========================== */
.banner {
  background-color: #1D3557;
  color: white;
  padding: 60px 30px;
  text-align: center;
}

.banner h1 {
  font-size: 3em;
  margin-bottom: 10px;
}

.banner p {
  font-size: 1.2em;
  color: #d1dbe9;
  margin-bottom: 30px;
}

.banner .cta-button {
  background-color: #F1FAEE;
  color: #1D3557;
  border: none;
  padding: 15px 30px;
  font-size: 1em;
  font-weight: 500;
  border-radius: 24px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.banner .cta-button:hover {
  background-color: #e6f0ec;
}


header {
  position: sticky;
  /* instead of fixed */
  top: 0;
  width: 100%;
  z-index: 1030;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

main#content-area {
  padding-top: 0;
  /* no extra padding needed */
}

/* ===========================
  Navbar Styles
  =========================== */
.navbar-custom {
  background-color: #0F2E5A;
}

.navbar-custom .nav-link,
.navbar-custom .navbar-brand {
  color: white;
}

.navbar-custom .navbar-nav .nav-link.active,
.navbar-nav .nav-link:hover {
  color: #d9d6f8;
  font-weight: bold;
  font-size: 1.3em;
  text-decoration: none;

}



#navbarNav.collapsing {
  height: auto !important;
}



/* Make sure the navbar uses flex layout */
.navbar .navbar-nav {
  display: flex;
  /* use flexbox */
  flex-wrap: nowrap;
  /* prevent wrapping */
  justify-content: center;
  /* center items horizontally */
  gap: 30px;
  /* space between items */
}

/* Optional: make container wider */
.navbar .container {
  max-width: 1800px;
  /* increase width */
}

/* ===========================
  See All link  Styles
  ===========================   */
.see-all-link {
  color: #000;
  /* dark black */
  font-weight: 700;
  /* bold */
  font-size: 1.25rem;
  /* larger text */
  text-transform: capitalize;
  letter-spacing: 1.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  text-decoration: none;
  transition: color 0.3s ease;
}

.see-all-link:hover {
  color: #0056b3;
  /* darker blue on hover */
  text-shadow: 2px 2px 4px rgba(0, 86, 179, 0.4);
  cursor: pointer;
}


/* ===========================
  Card & Image Styles
  =========================== */
/* Homepage books images */
.card-img-top {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.card[data-page] {
  cursor: pointer;
}

/* Homepage Explore more section */
.explore-img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.explore-card {
  height: 100%;
  min-height: 350px;
  display: flex;
  flex-direction: column;
  justify-content: start;
}

/* Books.html thumbnails */
.book-thumbnail {
  width: 100%;
  height: 220px;
  object-fit: cover;
  border-radius: 4px;
}

.book-row {
  display: flex;
  overflow-x: auto;
  gap: 1rem;
  padding-bottom: 1rem;
}

.book-card {
  min-width: 190px;
  flex: 0 0 auto;
}

.book-card img {
  height: 220px;
  object-fit: cover;
}

.btn-disabled {
  pointer-events: none;
  opacity: 0.6;
}

/* ===========================
  Events Section
  =========================== */
.events-img-wrapper {
  height: 180px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.events-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.events-card {
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  text-align: center;
  height: 100%;
}

/* ===========================
  Events highlight section
  =========================== */
#highlight-section {
  min-height: 200px;
}

/* ===========================
  Trendng & Popular Books  section
  =========================== */
#trending-section {
  min-height: 200px;
}

/* ===========================
  Search Bar
  =========================== */
#searchBar {
  opacity: 0;
  transform: scaleY(0);
  transform-origin: top;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

#searchBar.show {
  display: block !important;
  opacity: 1;
  transform: scaleY(1);
}

/* ===========================
  Footer
  =========================== */
footer {
  margin-top: auto;
}

.footer-touch-link {
  display: inline-block;
  padding: 2px 8px;
  margin-bottom: 2px;
  font-size: 1rem;
  min-width: 48px;
  min-height: 28px;
}

/* ===========================
  Add Pointer to all [data-page] elements
  ===========================  */
[data-page] {
  cursor: pointer;
}


/* ===========================
  Login Card
  =========================== */
.login-page {
  /* background: url('http://127.0.0.1:9000/media/books/book3.jpg') no-repeat center center fixed; */
  background-size: cover;
  height: 100vh;
  display: flex;
  justify-content: center;
  /* keeps it horizontally centered */
  align-items: flex-start;
  /* moves it to the top */
  padding-top: 100px;
  /* adjust based on header height */
}

.login-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.2);
  padding: 30px;
  width: 100%;
  max-width: 400px;
}

.login-card h3 {
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.form-control {
  border-radius: 10px;
}

.btn-custom {
  background: #4a5568;
  color: white;
  border-radius: 10px;
  transition: 0.3s;
}

.btn-custom:hover {
  background: #7687a3;
  color: rgb(255, 255, 255);
  border-radius: 10px;
  transition: 0.3s;
}

.logo {
  display: block;
  margin: 0 auto 20px;
  width: 80px;
}


/* Reusable Card Styles */
.form-container {

  background-size: cover;
  height: 200vh;
  display: flex;
  justify-content: center;
  /* keeps it horizontally centered */
  align-items: flex-start;
  /* moves it to the top */
  padding-top: 100px;
}

.card-password {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

/* Form group spacing */
.form-group {
  margin-bottom: 15px;
  /* adds vertical spacing between fields */
}

/* Input styling */
.form-control {
  width: 100%;
  padding: 8px 10px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

/* Button */
.btn-primary {
  background: #4a5568;
  color: white;
  border-radius: 10px;
  transition: 0.3s;
  align-items: center;
}

.btn-primary:hover {
  background-color: #0056b3;
}


/* Profile Picture */

.profile-picture {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #ddd;
}

/* Sign up Card */
.signup-card {
  max-width: 1200px;
  margin: 50px auto;
  padding: 40px;
  border-radius: 15px;
  border: 1px solid #dee2e6;
  /* subtle gray border */
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

/* Header styling */
.signup-card h3 {
  font-weight: 700;
  color: #343a40;
}

.signup-card p {
  color: #6c757d;
}

/* Labels */
.signup-card .form-label {
  font-weight: 600;
  color: #495057;
}

/* Required field asterisk */
.signup-card .form-label .text-danger {
  margin-left: 2px;
}

/* Input fields */
.signup-card .form-control,
.signup-card .form-select {
  border-radius: 10px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

/* Error text */
.signup-card .text-danger {
  font-size: 0.85rem;
}

/* Submit button */
.signup-card .btn-primary {
  border-radius: 50px;
  padding: 0.75rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.signup-card .btn-primary:hover {
  background-color: #0056b3;
}

/* Responsive spacing */
@media (max-width: 576px) {
  .signup-card {
    padding: 25px;
  }
}

/* admin form table header  */
.custom-header {
  background-color: #0F2E5A;
  /* your color */
  color: white;
  /* text color */
}

/* SilentProfile primary color */
.sl-text-primary {

  color: #0F2E5A;

}

/* for bootstrap tables */
.table-auto {
  table-layout: auto !important;
  width: 100% !important;
}
