<!-- userprofile.html -->

{% extends base_template %}  
{% load static %}
{% block title %}User Profile{% endblock %} {% block content %}
 <div class="container">
    <div class="profile-card">
      <div class="profile-header">
        {% if profile and profile.profile_picture %}
          <img src="{{ profile.profile_picture.url }}" class="profile-picture mb-3" alt="Profile Picture">
        {% else %}
         {% if profile.gender == 'M' %}
        <img src="/media/profile_pictures/avatar_male.jpg" alt="Male Avatar" class="profile-picture mb-3">
    {% else %}
        <img src="/media/profile_pictures/avatar_female.jpg" alt="Female Avatar" class="profile-picture mb-3">
    {% endif %}
        {% endif %}
        <h3>{{ user.get_full_name|default:user.username }}</h3>
        <p class="text-muted">@{{ user.username }}</p>
      </div>
      
      <hr>
      <h5>Account Information</h5>
      <ul class="list-group list-group-flush">
        <li class="list-group-item"><strong>Username:</strong> {{ user.username }}</li>
        <li class="list-group-item"><strong>Email:</strong> {{ user.email }}</li>
        <li class="list-group-item"><strong>First Name:</strong> {{ user.first_name }}</li>
        <li class="list-group-item"><strong>Last Name:</strong> {{ user.last_name }}</li>
        <li class="list-group-item"><strong>Bio:</strong> {{ profile.bio }}</li>
        <li class="list-group-item"><strong>Date Joined:</strong> {{ user.date_joined|date:"M d, Y" }}</li>
        <li class="list-group-item"><strong>Last Login:</strong> {{ user.last_login|date:"M d, Y H:i" }}</li>
      </ul>

      {% if profile %}
        <hr>
        <h5>Profile Information</h5>
        <ul class="list-group list-group-flush">
          <li class="list-group-item"><strong>Bio:</strong> {{ profile.bio }}</li>
        </ul>
      {% endif %}
      
      <div class="mt-4 text-center">
        {% comment %} <a href="{% url 'edit_profile' %}" class="btn btn-primary">Edit Profile</a> {% endcomment %}
        <a href="{% url 'logout' %}" class="btn btn-outline-danger">Logout</a>
      </div>
    </div>
  </div>
{% endblock %}