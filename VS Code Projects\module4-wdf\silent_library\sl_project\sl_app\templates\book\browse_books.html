{% extends 'main/base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4 text-center">Browse Books by Category</h2>

  {% for cat in category_books %}
<div class="mb-5">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="text-primary">{{ cat.genre.genre_name }}</h3>
        <a href="" class="btn btn-outline-secondary btn-sm">See All</a>
    </div>

    <div class="row row-cols-1 row-cols-md-3 row-cols-lg-6 g-4">
        {% for item in cat.books %}
        <div class="col">
            <div class="card h-100 shadow-sm">
                {% if item.book.cover_image %}
                    <img src="{{ item.book.cover_image.url }}" class="card-img-top" alt="{{ item.book.title }}">
                {% else %}
                    <img src="{% static 'images/default_book_cover.jpg' %}" class="card-img-top" alt="No cover">
                {% endif %}

                <div class="card-body d-flex flex-column">
                    <h6 class="card-title">{{ item.book.title }}</h6>
                    <p class="text-muted mb-2">{{ item.book.author.author_name }}</p>
                    <p class="small text-truncate">{{ item.book.description }}</p>

                    <div class="mt-auto">
                        {% if request.user.is_authenticated %}
                            {% if request.user.is_staff %}
                                <a href="{% url 'book_update' item.book.book_id %}" class="btn btn-warning btn-sm w-100">Edit</a>
                            {% else %}
                                {% if item.can_return %}
                                    <a href="{% url 'book_detail' item.book.book_id %}" class="btn btn-primary btn-sm w-100" >Loan Details</a>
                                    
                                {% elif item.can_borrow %}
                                    <a href="{% url 'book_detail' item.book.book_id %}" class="btn btn-success btn-sm w-100">Borrow</a>
                                {% else %}
                                    <button class="btn btn-secondary btn-sm w-100" disabled>Not Available</button>
                                {% endif %}
                            {% endif %}
                        {% else %}
                            <a href="{% url 'login' %}" class="btn btn-outline-primary btn-sm w-100">Login to Borrow</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endfor %}


    <div class="text-center mt-4">
        <a data-page="all_categories" class="btn btn-primary">Browse More Categories</a>
    </div>
</div>
{% endblock %}
