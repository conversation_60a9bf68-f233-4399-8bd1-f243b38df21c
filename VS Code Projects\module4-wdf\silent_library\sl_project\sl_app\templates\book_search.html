{% extends 'main/base.html' %}
{% block title %}Book Search{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">Search Books</h2>
    <form method="get" action="{% url 'book_search' %}" class="row g-3">
        <div class="col-md-8">
            <input type="text" name="q" value="{{ query }}" class="form-control" placeholder="Search by title, author, or genre">
        </div>
        <div class="col-md-4">
            <button type="submit" class="btn btn-primary flex-grow-1">Search</button>
            <button type="button" class="btn btn-secondary flex-grow-1" onclick="window.location.href='{{ request.path }}'">
            <i class="bi bi-x-circle"></i> Clear
        </button>
        </div>
    </form>

    <hr class="my-4">

    {% if books %}
    {% if books.object_list %}
        <h4 class="mb-3">Results:</h4>
        <div class="table-responsive">
            <table class="table table-striped table-hover align-middle shadow-sm rounded">
                <thead class="table-dark">
                    <tr>
                        <th>Title</th>
                        <th>Author</th>
                        <th>Genre</th>
                        <th>Publication Year</th>
                        <th>ISBN</th>
                        
                        <th>Cover</th>
                        <th>Availability</th>
                    </tr>
                </thead>
                <tbody>
                    {% for book in books %}
                    <tr>
                        <td>
                            <a href="{% url 'book_detail' book.book_id %}?q={{ query }}" class="text-decoration-none">
                                {{ book.title }}
                            </a>
                        </td>
                        <td>{{ book.author.author_name }}</td>
                        <td>{{ book.genre.genre_name }}</td>
                        <td>{{ book.publication_year }}</td>
                        <td>{{ book.isbn }}</td>
                        <td>
                                {% if book.cover_image %}
                                    <img src="{{ book.cover_image.url }}" alt="Cover" class="img-thumbnail" style="max-width:60px;">
                                {% else %}
                                    <span class="text-muted">No Cover</span>
                                {% endif %}
                            </td>
                        <td>
                            {% if book.availability %}
                                <span class="badge bg-success">Available</span>
                            {% else %}
                                <span class="badge bg-danger">Not Available</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if books.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?q={{ query }}&page=1">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?q={{ query }}&page={{ books.previous_page_number }}">Previous</a>
                    </li>
                {% endif %}

                <li class="page-item disabled">
                    <span class="page-link">Page {{ books.number }} of {{ books.paginator.num_pages }}</span>
                </li>

                {% if books.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?q={{ query }}&page={{ books.next_page_number }}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?q={{ query }}&page={{ books.paginator.num_pages }}">Last</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% else %}
        <div class="alert alert-warning mt-3" role="alert">
            No books found.
        </div>
    {% endif %}
    {% endif %}
</div>
{% endblock %}
