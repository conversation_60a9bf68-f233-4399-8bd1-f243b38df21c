<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description"
    content="Silent Library – A modern digital library for reading, borrowing, and exploring books and events.">

  <title>Silent Library</title>
  <link rel="icon" href="static/images/favicon.ico" type="image/ico" ">
<!-- Preconnect to font servers -->
  <link rel=" preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Preload Google Fonts CSS and load async -->
  <link rel="preload"
    href="https://fonts.googleapis.com/css2?family=Merriweather:wght@700&family=Open+Sans&display=swap" as="style"
    onload="this.onload=null;this.rel='stylesheet'">
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" as="style"
    onload="this.onload=null;this.rel='stylesheet'">
  <noscript>
    <link href="https://fonts.googleapis.com/css2?family=Merriweather:wght@700&family=Open+Sans&display=swap"
      rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
  </noscript>

  <!-- Load Bootstrap CSS asynchronously -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" media="print"
    onload="this.onload=null;this.media='all';">
  <noscript>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  </noscript>

  <!-- Load Bootstrap Icons normally (small size, OK to block) -->
 <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">


  <!-- Load jQuery  -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

  <!-- Custom Stylesheet (ensure minified) -->
  <link href="static/css/styles.css" rel="stylesheet">

  <!-- Preload the first banner image to improve initial load time -->
  <link rel="preload" as="image" href="static/images/banner4.jpg">

</head>

<body>

  <!-- Header Navbar -->
  <header>
    <nav class="navbar navbar-expand-lg navbar-custom">
      <div class="container position-relative" data-page="home">
        <a class="navbar-brand d-flex align-items-center" href="#">
          <img src="static/images/main-logo.jpg" alt="Silent Library Logo" width="98" height="70"
            style="margin-right: 40px;">

          Silent Library
        </a>
        <button class="navbar-toggler bg-light" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
          <span class="navbar-toggler-icon"></span>
        </button>
        <!-- Nav Links -->
        <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
          <ul class="navbar-nav align-items-lg-center">
            <li class="nav-item me-4">
              <a class="nav-link" href="#" data-page="home""><i class=" bi bi-house-door me-1"></i>Home</a>
            </li>
            <li class="nav-item me-4">
              <a class="nav-link" href="#" data-page="books""><i class=" bi bi-book me-1"></i>Books</a>
            </li>
            <li class="nav-item me-4">
              <a class="nav-link" href="#" data-page="events""><i class=" bi bi-calendar-event me-1"></i>Events</a>
            </li>
            <li class="nav-item me-4">
              <a class="nav-link" href="#" data-page="contact""><i class=" bi bi-envelope me-1"></i>Contact Us</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="searchIcon"  href="#"><i class="bi bi-search me-1"></i>Search</a>
            </li>
          </ul>
        </div>



        <!-- Search Bar -->
        <!-- Search Bar -->
        <div id="searchBar" class="position-fixed top-0 start-50 translate-middle-x p-3 bg-white rounded shadow d-none"
          style="z-index: 1050; width: 75vw; max-width: 700px; animation: fadeIn 0.3s ease-in-out;">
          <!-- Search input with icon -->
          <div class="row g-2 mb-3">
            <div class="col-12 col-sm-1 d-flex align-items-center justify-content-start">
              <span class="input-group-text bg-white border-0"><i class="bi bi-search"></i></span>
            </div>
            <div class="col-12 col-sm-7">
              <input type="text" class="form-control" placeholder="Search books by Title, author, topic..."
                aria-label="Search books input field" />
            </div>
            <div class="col-6 col-sm-2">
              <button class="btn btn-primary w-100" type="button" id="searchBtn" aria-label="Search books">
                Search
              </button>
            </div>
            <div class="col-6 col-sm-2">
              <button class="btn btn-outline-secondary w-100" type="button" id="closeSearch"
                aria-label="Close search bar">
                <i class="bi bi-x-lg"></i>
              </button>
            </div>
          </div>



          <!-- Advanced Search link -->
          <div class="text-end">
            <a href="#" class="text-decoration-none small text-primary" data-page="advanced_search">Advanced Search</a>
          </div>
        </div>




      </div>
      </div>
    </nav>
  </header>


  <!-- Dynamic Content Area -->
  <main class="container-fluid p-0" id="content-area">
    <!-- Content will load here -->
  </main>

  <!-- Footer -->
  <footer id="footer-area" class="bg-dark text-white mt-5 pt-4 pb-3" style="min-height: 150px; visibility: hidden;">


    <div class="container">
      <div class="row text-center text-md-start">

        <!-- Column 1: Navigation Links -->
        <div class="col-md-4 mb-4">
          <h6 class="text-uppercase">Quick Links</h6>
          <ul class="list-unstyled small">
            <li><a href="sitemap.html" data-page="sitemap"
                class="footer-touch-link text-white text-decoration-none">Sitemap</a></li>
            <li><a href="privacy.html" data-page="privacy"
                class="footer-touch-link text-white text-decoration-none">Privacy Policy</a>
            </li>
            <li><a href="contact.html" data-page="contact"
                class="footer-touch-link text-white text-decoration-none">Contact Us</a></li>
          </ul>
        </div>

        <!-- Column 2: Address & Hours -->
        <div class="col-md-4 mb-1">
          <h6 class="text-uppercase">Library Info</h6>
          <address class="small">
            Silent Library<br>
            123 Knowledge Lane<br>
            Singapore 567890<br>
            <span>Mon–Fri: 9:00am – 6:00pm</span><br>
            <span>Sat: 10:00am – 4:00pm</span>
          </address>
        </div>

        <!-- Column 3: Contact & Social -->
        <div class="col-md-4 mb-1 text-md-end">
          <h6 class="text-uppercase">Connect</h6>
          <p class="small mb-2">
            📧 <a href="mailto:<EMAIL>"
              class="text-white text-decoration-none"><EMAIL></a><br>
            📞 <a href="tel:+6567891234" class="text-white text-decoration-none">+65 6789 1234</a>
          </p>
          <div>
            <a href="#" class="text-white me-2" aria-label="Facebook"><i class="bi bi-facebook"></i></a>
            <a href="#" class="text-white me-2" aria-label="X"><i class="bi bi-twitter-x"></i></a>
            <a href="#" class="text-white" aria-label="Instagram"><i class="bi bi-instagram"></i></a>
          </div>
        </div>

      </div>

      <!-- Bottom Row: Back to Top and Copyright -->
      <div class="row border-top pt-1 mt-1">
        <div class="col-md-6 text-center text-md-start small">
          © 2025 Silent Library. All rights reserved.
        </div>
        <div class="col-md-6 text-center text-md-end small">
          <a href="#" class="text-white text-decoration-none">Back to top ↑</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Load Initial Home Page -->
  <script>
    // Fetch and load home.html into main
    fetch('home')
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.text();
      })
      .then(html => {
        document.getElementById('content-area').innerHTML = html;
        document.getElementById('footer-area').style.visibility = 'visible';// show footer only after content is ready
      })
      .catch(error => {
        console.error('Failed to load home.html:', error);
        document.getElementById('content-area').innerHTML = '<p>Error loading content.</p>';
        document.getElementById('footer').style.display = 'block';
      });
  </script>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="static/scripts/scripts.js"></script>
</body>

</html>