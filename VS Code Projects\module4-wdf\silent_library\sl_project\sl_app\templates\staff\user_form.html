{% extends 'main/staff_base.html' %}
{% block content %}
<div class="container mt-10">
    <div class="card shadow rounded-3 mx-auto p-3" style="max-width: 900px;"> <!-- Added padding + centered + max width -->
        <div class="card-header custom-header  text-white">
            <h4 class="mb-0">{% if not user or user.mode == 'new'  %}✏️ ADD USER{% else %}➕ EDIT USER{% endif %}</h4>
        </div>
        <div class="card-body p-4 bg-light">  <!-- Light background + padding -->
            <form method="POST" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="row g-3">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Username</label>
                        <input type="text" name="username" class="form-control" required
                               value="{{ user.username|default:'' }}" >
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-control" required
                               value="{{ user.email|default:'' }}">
                    </div>

<div class="col-md-6 mb-3">
                        <label class="form-label">First Name</label>
                        <input type="text" name="first_name" class="form-control" required
                               value="{{ user.first_name|default:'' }}" >
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">Last Name</label>
                        <input type="text" name="last_name" class="form-control" required
                               value="{{ user.last_name|default:'' }}">
                    </div>


                    <div class="col-md-6 mb-3">
                        <label class="form-label">Gender</label>
                        {% with gender=profile.gender %}
                        <select name="gender" class="form-select" required>
                            <option value="" {% if not gender %}selected{% endif %}>Select Gender</option>
                            <option value="M" {% if gender == "M" %}selected{% endif %}>Male</option>
                            <option value="F" {% if gender == "F" %}selected{% endif %}>Female</option>
                        </select>
                        {% endwith %}
                    </div>

                    <div class="col-md-12 mb-3">
                        <label class="form-label">Bio</label>
                        <textarea name="bio" class="form-control" rows="4">{{ profile.bio|default:'' }}</textarea>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label class="form-label">Profile Picture</label>
                        <input type="file" name="profile_picture" class="form-control">
                        {% if profile.profile_picture %}
                            <img src="{{ profile.profile_picture.url }}" alt="Profile Picture" class="img-thumbnail mt-2" style="max-width:150px;">
                        {% endif %}
                    </div>
                </div>

                <div class="d-flex gap-2 mt-4 justify-content-center">
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-save"></i> Save
                    </button>
                    <a href="javascript:history.back()" class="btn btn-secondary ms-2">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
