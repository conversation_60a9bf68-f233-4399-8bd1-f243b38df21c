from datetime import timedelta, timezone
from django.db import models
from django.contrib.auth.models import *

# Create your models here.
class UserProfile(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
    ]

    user = models.OneToOneField(User, on_delete=models.RESTRICT)
    bio = models.TextField(blank=True)
    profile_picture = models.ImageField(upload_to='profile_pictures/', blank=True, null=True)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, blank=True)
    
def __str__(self):
        return self.user.username

class Author(models.Model):
    author_id = models.AutoField(primary_key=True)
    author_name = models.CharField(max_length=100, db_index=True)
    author_biography = models.TextField(blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    nationality = models.CharField(max_length=100, blank=True, null=True)

    created_at = models.DateTimeField( default=timezone.now, editable=False)
    updated_at = models.DateTimeField( default=timezone.now)

    class Meta:
        ordering = ['author_name']
        indexes = [
            models.Index(fields=['author_name']),
        ]

    def save(self, *args, **kwargs):
        if not self.created_at:
            self.created_at = timezone.now()  # set once when object is created
        self.updated_at = timezone.now()      # update every save
        super().save(*args, **kwargs)
        
    def __str__(self):
        return self.author_name

class Genre(models.Model):
    genre_id = models.AutoField(primary_key=True)
    genre_name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)

    created_at = models.DateTimeField( default=timezone.now,editable=False)
    updated_at = models.DateTimeField(default=timezone.now)
    class Meta:
        ordering = ['genre_name']
        indexes = [
            models.Index(fields=['genre_name']),           
        ]
    def save(self, *args, **kwargs):
        if not self.created_at:
            self.created_at = timezone.now()  # set once when object is created
        self.updated_at = timezone.now()      # update every save
        super().save(*args, **kwargs)        
    def __str__(self):
        return self.genre_name

class Book(models.Model):
    book_id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=200, db_index=True)
    author = models.ForeignKey(Author, on_delete=models.RESTRICT, related_name='books')
    publication_year = models.PositiveSmallIntegerField()
    isbn = models.CharField(max_length=20, unique=True)
    description = models.TextField()
    total_copies = models.PositiveIntegerField()
    copies_available = models.PositiveIntegerField()
    genre = models.ForeignKey(Genre, on_delete=models.RESTRICT, related_name='books')
    availability = models.BooleanField(default=True)
    cover_image = models.ImageField(upload_to='books/', blank=True, null=True)
    created_at = models.DateTimeField( default=timezone.now,editable=False)
    updated_at = models.DateTimeField( default=timezone.now)

    class Meta:
        ordering = ['-book_id']
        indexes = [
            models.Index(fields=['title']),
            models.Index(fields=['isbn']),
            models.Index(fields=['publication_year']),
        ]
        
    def save(self, *args, **kwargs):
        if not self.created_at:
            self.created_at = timezone.now()  # set once when object is created
        self.updated_at = timezone.now()      # update every save
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.title} ({self.publication_year})"
    
def default_due_date():
    return timezone.now() + timedelta(days=14)

class Loan(models.Model):
    loan_id = models.AutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.RESTRICT)
    book = models.ForeignKey("Book", on_delete=models.RESTRICT)

    borrowed_at = models.DateTimeField(default=timezone.now)
    due_date = models.DateTimeField(default=default_due_date)  # 14 days from borrow date
    returned_at = models.DateTimeField(null=True, blank=True)

    fine_amount = models.DecimalField(max_digits=6, decimal_places=2, default=0)

    def calculate_fine(self):
        ref_date = self.returned_at or timezone.now()
        overdue_seconds = (ref_date - self.due_date).total_seconds()
        if overdue_seconds > 0:
            days_overdue = int(overdue_seconds // 86400)  # 86400 seconds = 1 day
            return days_overdue * 1  # $1 per day
        return 0


    def settle_fine(self):
        self.fine_amount = self.calculate_fine()
        self.save()

    @property
    def is_overdue(self):
        return self.returned_at is None and timezone.now() > self.due_date

    def __str__(self):
        status = "Returned" if self.returned_at else "Borrowed"
        return f"{self.user.username} - {self.book.title} ({status})"
    

class Review(models.Model):
    RATING_CHOICES = [
        (1, '1 - Very Poor'),
        (2, '2 - Poor'),
        (3, '3 - Average'),
        (4, '4 - Good'),
        (5, '5 - Excellent'),
    ]

    review_id = models.AutoField(primary_key=True)
    book = models.ForeignKey('Book', on_delete=models.CASCADE, related_name='reviews')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    rating = models.IntegerField(choices=RATING_CHOICES)
    comment = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.book.title} - {self.user.username} ({self.rating})"