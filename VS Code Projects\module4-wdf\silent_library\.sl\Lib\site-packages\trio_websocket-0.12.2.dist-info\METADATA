Metadata-Version: 2.1
Name: trio-websocket
Version: 0.12.2
Summary: WebSocket library for Trio
Home-page: https://github.com/python-trio/trio-websocket
Author: <PERSON>
Author-email: <EMAIL>
Project-URL: Bug Reports, https://github.com/python-trio/trio-websocket/issues
Project-URL: Source, https://github.com/python-trio/trio-websocket
Keywords: websocket client server trio
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Libraries
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Typing :: Typed
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: outcome >=1.2.0
Requires-Dist: trio >=0.11
Requires-Dist: wsproto >=0.14
Requires-Dist: exceptiongroup ; python_version < "3.11"

# Trio WebSocket

This library implements both server and client aspects of the [the WebSocket
protocol](https://tools.ietf.org/html/rfc6455), striving for safety,
correctness, and ergonomics. It is based on the [wsproto
project](https://wsproto.readthedocs.io/en/latest/), which is a
[Sans-IO](https://sans-io.readthedocs.io/) state machine that implements the
majority of the WebSocket protocol, including framing, codecs, and events. This
library handles I/O using [the Trio
framework](https://trio.readthedocs.io/en/latest/). This library passes the
[Autobahn Test Suite](https://github.com/crossbario/autobahn-testsuite).

This README contains a brief introduction to the project. Full documentation [is
available here](https://trio-websocket.readthedocs.io).

[![PyPI](https://img.shields.io/pypi/v/trio-websocket.svg?style=flat-square)](https://pypi.org/project/trio-websocket/)
![Python Versions](https://img.shields.io/pypi/pyversions/trio-websocket.svg?style=flat-square)
[![Build Status](https://img.shields.io/github/actions/workflow/status/python-trio/trio-websocket/ci.yml)](https://github.com/python-trio/trio-websocket/actions/workflows/ci.yml)
[![Read the Docs](https://img.shields.io/readthedocs/trio-websocket.svg)](https://trio-websocket.readthedocs.io)
[![Join chatroom](https://img.shields.io/badge/chat-join%20now-blue.svg)](https://gitter.im/python-trio/general)

## Status
**This project is on life-support maintenance.** If you're interested in helping to maintain and contribute, please reach out on [Gitter](https://gitter.im/python-trio/general) or contribute in issues and pull requests.

## Alternatives

If you happen to only need a server, using Quart via the [quart-trio](https://github.com/pgjones/quart-trio)
extension may suffice.  While trio-websocket is more flexible, Quart covers
both HTTP and WebSocket within a single framework, and serving both from the
same port is straightforward.  There has yet to be a performance comparison.

## Installation

This library requires Python 3.8 or greater. To install from PyPI:

    pip install trio-websocket

## Client Example

This example demonstrates how to open a WebSocket URL:

```python
import trio
from sys import stderr
from trio_websocket import open_websocket_url


async def main():
    try:
        async with open_websocket_url('wss://echo.websocket.org') as ws:
            await ws.send_message('hello world!')
            message = await ws.get_message()
            print('Received message: %s' % message)
    except OSError as ose:
        print('Connection attempt failed: %s' % ose, file=stderr)

trio.run(main)
```

The WebSocket context manager connects automatically before entering the block
and disconnects automatically before exiting the block. The full API offers a
lot of flexibility and additional options.

## Server Example

A WebSocket server requires a bind address, a port, and a coroutine to handle
incoming connections. This example demonstrates an "echo server" that replies to
each incoming message with an identical outgoing message.

```python
import trio
from trio_websocket import serve_websocket, ConnectionClosed

async def echo_server(request):
    ws = await request.accept()
    while True:
        try:
            message = await ws.get_message()
            await ws.send_message(message)
        except ConnectionClosed:
            break

async def main():
    await serve_websocket(echo_server, '127.0.0.1', 8000, ssl_context=None)

trio.run(main)
```

The server's handler ``echo_server(…)`` receives a connection request object.
This object can be used to inspect the client's request and modify the
handshake, then it can be exchanged for an actual WebSocket object ``ws``.
Again, the full API offers a lot of flexibility and additional options.
