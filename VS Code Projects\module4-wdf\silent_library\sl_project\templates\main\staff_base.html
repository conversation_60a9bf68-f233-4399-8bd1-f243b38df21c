<!-- templates/admin_base.html -->
{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}Admin Dashboard{% endblock %}</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
  <link href="{% static 'css/styles.css' %}" rel="stylesheet">
</head>
<body>
  <header>
    <nav class="navbar navbar-expand-lg navbar-custom">
      <div class="container position-relative" data-page="staff">
        <a class="navbar-brand d-flex align-items-center" href="#"><img src="{% static 'images/main-logo.jpg' %}" alt="Silent Library Logo" 
          width="98" height="70" 
          style="margin-right: 40px;">Silent Library (Staff Space)</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#adminNavbar"><span class="navbar-toggler-icon">

        </span></button>
        <div class="collapse navbar-collapse justify-content-center" id="adminNavbar">
          <ul class="navbar-nav align-items-center">
            <li class="nav-item"><a data-page="staff_dashboard" class="nav-link {% if request.path == '/admin/' %}active{% endif %}">
              <i class="bi bi-speedometer2"></i> Dashboard</a></li>
            <li class="nav-item"><a data-page="user_list" class="nav-link {% if request.path == '/admin/users/' %}active{% endif %}">
              <i class="bi bi-people"></i> Users</a></li>
            <li class="nav-item"><a data-page="book_list" class="nav-link {% if request.path == '/admin/books/' %}active{% endif %}">
              <i class="bi bi-book"></i> Books</a></li>
            <li class="nav-item"><a data-page="profile" class="nav-link {% if request.path == '/profile/' %}active{% endif %}">
              <i class="bi bi-person-circle"></i> My Profile</a></li>
            <li class="nav-item"><a data-page="home" class="nav-link {% if request.path == '/admin/' %}active{% endif %}">
              <i class="bi bi-house-door"></i> Silent Library Home</a></li>
            <li class="nav-item"><a data-page="logout" class="nav-link"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
          </ul>
        </div>
      </div>
    </nav>
  </header>
  <div id="admin-content" class="container mt-4">
    {% if messages %}
      <div class="mt-3">
        {% regroup messages by tags as grouped_messages %}
        {% for group in grouped_messages %}
          {% if group.grouper == "error" %}
            <div class="alert alert-danger" role="alert">{% for message in group.list %}<p class="mb-0">{{ message }}</p>{% endfor %}</div>
          {% elif group.grouper == "success" %}
            <div class="alert alert-success" role="alert">{% for message in group.list %}<p class="mb-0">{{ message }}</p>{% endfor %}</div>
          {% elif group.grouper == "warning" %}
            <div class="alert alert-warning" role="alert">{% for message in group.list %}<p class="mb-0">{{ message }}</p>{% endfor %}</div>
          {% elif group.grouper == "info" %}
            <div class="alert alert-info" role="alert">{% for message in group.list %}<p class="mb-0">{{ message }}</p>{% endfor %}</div>
          {% else %}
            <div class="alert alert-secondary" role="alert">{% for message in group.list %}<p class="mb-0">{{ message }}</p>{% endfor %}</div>
          {% endif %}
        {% endfor %}
      </div>
    {% endif %}
    {% block content %}{% endblock %}
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="{% static 'scripts/scripts.js' %}"></script>
</body>
</html>
