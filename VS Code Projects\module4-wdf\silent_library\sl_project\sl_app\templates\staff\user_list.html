{% extends 'main/staff_base.html' %}
{% block content %}
<div class="container mt-10">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="fw-bold sl-text-primary">👥 Manage Public Users</h1>
        <a href="{% url 'user_create' %}" class="btn btn-success">
            <i class="bi bi-plus-circle"></i> Add New User
        </a>
    </div>

    <div class="mb-3">
        <form method="get" class="row g-2">
            <div class="col-md-4">
                <input type="text" name="username" class="form-control" placeholder="Search by Username" value="{{ request.GET.username }}">
            </div>
            <div class="col-md-4">
                <input type="text" name="email" class="form-control" placeholder="Search by Email" value="{{ request.GET.email }}">
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary w-100"><i class="bi bi-search"></i> Search</button>
            </div>
        </form>
    </div>

    <div class="card1 shadow rounded-3">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-dark">
                        <tr>
                            <th>Username</th>
                            <th>Email</th>
                            <th>First Name</th>
                            <th>Last Name</th>
                            <th>Active</th>
                            <th>Date Joined</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.username }}</td>
                            <td>{{ user.email }}</td>
                            <td> {{ user.first_name }} </td>
                            <td> {{ user.last_name }} </td>
                            <td> {{ user.is_active }} </td>
                            <td> {{ user.date_joined }} </td>
                            <td class="text-end">
                                <a href="{% url 'user_edit' user.id %}" class="btn btn-warning btn-sm me-1">
                                    <i class="bi bi-pencil-square"></i> Edit
                                </a>
                                <a href="{% url 'user_delete' user.id %}" class="btn btn-danger btn-sm"
                                   onclick="return confirm('Are you sure you want to delete this user?');">
                                    <i class="bi bi-trash"></i> Delete
                                </a>
                                {% if user.is_active %}
                                <a href="{% url 'user_deactivate' user.id %}" class="btn btn-secondary btn-sm" onclick="return confirm('Are you sure you want to deactivate user - {{ user.username }}?');">
                                    <i class="bi bi-x-circle"></i> Deactivate
                                </a>
                                {% else %}
                                <a href="{% url 'user_activate' user.id %}"" class="btn btn-success btn-sm" onclick="return confirm('Are you sure you want to activate user - {{ user.username }}?');">
                                    <i class="bi bi-check-circle"></i> Activate
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="3" class="text-center">No public users found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
