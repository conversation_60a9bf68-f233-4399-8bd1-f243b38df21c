# Generated by Django 5.2.5 on 2025-08-28 03:00

from django.db import migrations
from django.contrib.auth.hashers import make_password
from django.contrib.auth.models import User


def reset_users(apps, schema_editor):
    for user in User.objects.all().filter(is_superuser=False ,is_staff=False):
        user.password = make_password('password')
        user.save()



class Migration(migrations.Migration):

    dependencies = [
        ('sl_app', '0009_alter_author_options_alter_book_options_and_more'),
    ]

    operations = [migrations.RunPython(reset_users)
    ]
