{% extends 'main/staff_base.html' %}
{% block title %}Staff Dashboard{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1 class="fw-bold sl-text-primary mb-4">👩‍💼 Staff Dashboard</h1>

    <div class="row mb-4">
        <div class="col-md-4">
    <div class="card text-white bg-info shadow"  style="height:180px;">
        <div class="card-body">
            <h5 class="card-title">Welcome, {{ request.user.first_name }}!</h5>
            <p class="card-text card-text display-6">Good day to you!!</p>
            <a href="{% url 'profile' %}" class="btn btn-light btn-sm">View Profile</a>
            <a href="{% url 'edit_profile' %}" class="btn btn-light btn-sm">Edit Profile</a>
            <a href="{% url 'logout' %}" class="btn btn-light btn-sm">Logout</a>
        </div>
    </div>
</div>

        <div class="col-md-4">
            <div class="card text-white bg-primary shadow"  style="height:180px;">
                <div class="card-body">
                    <h5 class="card-title">Total Books</h5>
                    <p class="card-text display-6">{{ total_books }}</p>
                    <a href="{% url 'book_list' %}" class="btn btn-light btn-sm">Manage Books</a>
                    <a href="{% url 'book_create' %}" class="btn btn-light btn-sm">Add Book</a>
                    
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-white bg-success shadow"  style="height:180px;">
                <div class="card-body">
                    <h5 class="card-title">Total Public Users</h5>
                    <p class="card-text display-6">{{ total_public_users }}</p>
                    <a href="{% url 'user_list' %}" class="btn btn-light btn-sm">Manage Users</a>
                    <a href="{% url 'user_create' %}" class="btn btn-light btn-sm">Add User</a>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}
