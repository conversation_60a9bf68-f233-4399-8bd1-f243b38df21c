import random
import re
import string
from django.shortcuts import get_object_or_404, redirect, render
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.urls import reverse
from .models  import *
from .forms import *
from django.core.mail import send_mail #email
from django.conf import settings 
from django.contrib.auth.decorators import login_required, user_passes_test
from django.utils.crypto import get_random_string
from django.core.paginator import Paginator
from django.db.models import Q
from django.contrib.auth.views import LoginView
from django.contrib.auth.forms import AuthenticationForm

# Only allow staff users
def staff_required(view_func):
    return user_passes_test(lambda u: u.is_staff, login_url='login')(view_func)

@staff_required
def staff_home(request):
    return render(request, 'staff_dashboard.html')

@staff_required
def staff_dashboard(request):
    #Get the total number of books and public users
    total_books = Book.objects.count()
    total_public_users = User.objects.filter(is_staff=False).count()
    context = {
        'total_books': total_books,
        'total_public_users': total_public_users,
    }
    return render(request, 'staff/staff_dashboard.html', context)

@staff_required
def book_list(request):
    books = Book.objects.all().order_by('-book_id')

    title = request.GET.get('title', '')
    author = request.GET.get('author', '')
    genre = request.GET.get('genre', '')

    if title:
        books = books.filter(title__icontains=title)
    if author:
        books = books.filter(author__author_name__icontains=author)
    if genre:
        books = books.filter(genre__genre_name__icontains=genre)

    paginator = Paginator(books, 10)  # 10 books per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
    }
    return render(request, 'staff/book_list.html', context)

@staff_required
def book_create(request):
    if request.method == 'POST':
        # Create a new unsaved book object with request values
        new_book = Book(
            title=request.POST.get('title', '').strip(),
            publication_year=request.POST.get('publication_year').strip(),
            cover_image=request.FILES.get('cover_image'),
            description=request.POST.get('description', '').strip(),
            total_copies=request.POST.get('total_copies') or 0,
            copies_available=request.POST.get('copies_available') or 0,
            isbn=request.POST.get('isbn', '').strip(),
            availability=(request.POST.get('availability') == 'on')
        )

        # Handle ForeignKey fields
        author_id = request.POST.get('author')
        genre_id = request.POST.get('genre')

        errors = []

        # Required fields validation
        if not new_book.title:
            errors.append("Title is required.")
        if not author_id:
            errors.append("Author is required.")
        if not genre_id:
            errors.append("Genre is required.")
        if  not new_book.publication_year or new_book.publication_year.isdigit()==False:
            errors.append("Publication year should be a valid 4-digit number.")
        if not new_book.isbn:
            errors.append("ISBN is required.")
        if not new_book.description:
            errors.append("Description is required.")

        # Validate foreign keys
        try:
            new_book.author = Author.objects.get(pk=author_id) if author_id else None
        except Author.DoesNotExist:
            errors.append("Selected author does not exist.")

        try:
            new_book.genre = Genre.objects.get(pk=genre_id) if genre_id else None
        except Genre.DoesNotExist:
            errors.append("Selected genre does not exist.")

        # Validate total copies & copies available
        try:
            new_book.total_copies = int(new_book.total_copies)
            new_book.copies_available = int(new_book.copies_available)
            if new_book.total_copies < new_book.copies_available:
                errors.append("Total copies cannot be less than copies available.")
        except ValueError:
            errors.append("Total copies and copies available must be valid numbers.")

        # Check ISBN uniqueness
        if Book.objects.filter(isbn=new_book.isbn).exists():
            errors.append("ISBN already exists.")

        if errors:
            # Send the unsaved book back to the template for value retention
            for e in errors:
                messages.error(request, e)
            return render(
                request,
                "staff/book_form.html",
                {
                    "book": new_book,
                    "authors": Author.objects.all(),
                    "genres": Genre.objects.all(),
                }
            )

        # Save if everything is valid
        new_book.save()
        messages.success(request, "Book created successfully!")
        return redirect("book_list")

    # GET request: show empty form
    return render(
        request,
        "staff/book_form.html",
        {"authors": Author.objects.all(), "genres": Genre.objects.all() },
    )

@staff_required
def book_update(request, pk):
    book = get_object_or_404(Book, pk=pk)

    if request.method == 'POST':
        #This gets the current book cover image and sets it to new book. All the other values will be updated from the form
        book = get_object_or_404(Book, pk=pk)
        cover_image = book.cover_image  
        # create an unsaved "draft" book object with form values
        new_book = Book(
            title=request.POST.get('title', '').strip(),
            author_id=request.POST.get('author'),   # since it's FK
            genre_id=request.POST.get('genre'),     # since it's FK
            publication_year=request.POST.get('publication_year') or None,
            description=request.POST.get('description', '').strip(),
            total_copies=request.POST.get('total_copies') or 0,
            copies_available=request.POST.get('copies_available') or 0,
            isbn=request.POST.get('isbn', '').strip(),
            availability=(request.POST.get('availability') == "on"),
            cover_image=cover_image
        )

        # --- Validation ---
        errors = []

        # Required fields
        required_fields = {
            "Title": new_book.title,
            "Author": new_book.author_id,
            "Genre": new_book.genre_id,
            "Publication Year": new_book.publication_year,
            "Description": new_book.description,
            "ISBN": new_book.isbn,
        }
        for field, value in required_fields.items():
            if not value:
                errors.append(f"{field} is required.")

        # Publication year
        if new_book.publication_year and not str(new_book.publication_year).isdigit():
            errors.append("Publication year must be a valid 4-digit number.")

        # Copies check
        try:
            total = int(new_book.total_copies)
            available = int(new_book.copies_available)
            if total < available:
                errors.append("Total copies cannot be less than copies available.")
        except ValueError:
            errors.append("Copies must be valid numbers.")

        # ISBN uniqueness
        if Book.objects.filter(isbn=new_book.isbn).exclude(pk=pk).exists():
            errors.append("ISBN already exists.")

        if errors:
            for e in errors:
                messages.error(request, e)
            
            return render(
                request,
                "staff/book_form.html",
                {"book": new_book, "authors": Author.objects.all(), "genres": Genre.objects.all()},
            )

        # --- Save changes to original book ---
        book.title = new_book.title
        book.author_id = new_book.author_id
        book.genre_id = new_book.genre_id
        book.publication_year = int(new_book.publication_year)
        book.description = new_book.description
        book.total_copies = new_book.total_copies
        book.copies_available = new_book.copies_available
        book.isbn = new_book.isbn
        book.availability = new_book.availability

        if request.FILES.get('cover_image'):
            book.cover_image = request.FILES['cover_image']

        book.save()
        messages.success(request, "Book updated successfully.")
        return redirect("book_list")

    return render(
        request,
        "staff/book_form.html",
        {"book": book, "authors": Author.objects.all(), "genres": Genre.objects.all()},
    )

@staff_required
def book_delete(request, pk):
    book = get_object_or_404(Book, pk=pk)
    if request.method == 'POST':
        book.delete()
        messages.success(request, "Book deleted successfully.") 
        return redirect('book_list')
    return render(request, 'staff/book_confirm_delete.html', {'book': book})

@staff_required
def user_list(request):
    users = User.objects.filter(is_staff=False)  # only public users

    username = request.GET.get('username')
    email = request.GET.get('email')

    if username:
        users = users.filter(username__icontains=username)
    if email:
        users = users.filter(email__icontains=email)

    return render(request, 'staff/user_list.html', {
        'users': users,
    })

@staff_required
def user_create(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        email = request.POST.get('email')
        gender = request.POST.get('gender')
        bio = request.POST.get('bio')
        profile_picture = request.FILES.get('profile_picture')

        errors = []
        # validate username
        if User.objects.filter(username=username).exists():
            errors.append("Username already exists")

        # validate email
        email_regex = r'^[\w\.-]+@[\w\.-]+\.\w+$'
        if not re.match(email_regex, email or ""):
            errors.append("Invalid email address")
        elif User.objects.filter(email=email).exists():
            errors.append("Email already exists")

        if errors:
            # prepare a user object for redisplay if error occurs
            new_user = User(username=username, email=email, first_name=first_name, last_name=last_name)
            new_user.mode = 'new' 
            new_profile = UserProfile(gender=gender, bio=bio, profile_picture=profile_picture)
            for error in errors:
                messages.error(request, error)
            return render(request, 'staff/user_form.html', {'user': new_user , 'profile': new_profile})

        # generate random password
        password = ''.join(random.choices(string.ascii_letters + string.digits, k=10))

        # create the publicuser
        user = User.objects.create_user(username=username, email=email, password=password,first_name=first_name,
            last_name=last_name,    )

        # now create a profile for this user
        UserProfile.objects.create(
            user=user,
            gender=gender,
            bio=bio,
            profile_picture=profile_picture if profile_picture else None
        )

        # send email with password
        send_mail(
            subject="Your Silent Library Account",
            message=f"Hello {username},\n\nYour account has been created.\n Your login credentials are:\nUsername:{username}\nYour temporary login password is: {password}\n\nPlease change it after first login.",
            from_email="<EMAIL>",
            # recipient_list=[email],
            recipient_list=["<EMAIL>"],
            fail_silently=False,
        )

        messages.success(request, "User created successfully")
        return redirect('user_list')

    return render(request, 'staff/user_form.html', {'user': None})

@staff_required
def user_edit(request, user_id):
    user = get_object_or_404(User, pk=user_id, is_staff=False)
    profile, created = UserProfile.objects.get_or_create(user=user)
          
    if request.method == 'POST':
        username = request.POST.get('username', '').strip()
        email = request.POST.get('email', '').strip()
        first_name = request.POST.get('first_name', '').strip()
        last_name = request.POST.get('last_name', '').strip()
        gender = request.POST.get('gender')
        bio = request.POST.get('bio')
        
        #This sets the profile picture to the current profile picture if the user does not upload a new one
        if  request.FILES.get('profile_picture'):
            profile_picture = request.FILES.get('profile_picture')
        else:
            profile_picture = profile.profile_picture
        
        errors = []

        # Username validation
        if not username:
            errors.append("Username is required.")
        elif User.objects.exclude(pk=user.id).filter(username=username).exists():
            errors.append("This username is already taken.")

        # Email validation
        if not email:
            errors.append("Email is required.")
        elif not re.match(r"[^@]+@[^@]+\.[^@]+", email):
            errors.append("Invalid email format.")
        elif User.objects.exclude(pk=user.id).filter(email=email).exists():
            errors.append("This email is already registered.")

        # If there are errors, return with context
        if errors:
            # prepare a user object for redisplay if error occurs
            new_user = User(username=username, email=email, first_name=first_name, last_name=last_name)
            new_user.mode = 'edit'
            new_profile = UserProfile(gender=gender, bio=bio, profile_picture=profile_picture)
            for error in errors:
                messages.error(request, error)
           
            return render(request, 'staff/user_form.html', {
                'user': new_user,
                'profile': new_profile
                
            })

        # Save valid data
        user.username = username
        user.email = email
        user.first_name = first_name
        user.last_name = last_name
        profile.gender = gender
        profile.bio = bio
        profile.profile_picture = profile_picture
        
        user.save()
        profile.save()

        messages.success(request, "User updated successfully")
        return redirect('user_list')

    return render(request, 'staff/user_form.html', {'user': user, 'profile': profile})

@staff_required
def user_activate(request, user_id):
    user = get_object_or_404(User, pk=user_id, is_staff=False)
    user.is_active = True
    user.save()
    messages.success(request, "User activated successfully")
    return redirect('user_list')

@staff_required
def user_deactivate(request, user_id):
    user = get_object_or_404(User, pk=user_id, is_staff=False)
    user.is_active = False
    user.save()
    messages.success(request, "User deactivated successfully")
    return redirect('user_list')

@staff_required
def user_delete(request, user_id):
    user = get_object_or_404(User, pk=user_id, is_staff=False)
    user.delete()
    messages.success(request, "User deleted successfully")
    return redirect('user_list')

#---This is to handle the login and redirect to the respective page
class CustomLoginView(LoginView):
    template_name = 'login.html'
    
    authentication_form = AuthenticationForm

    def form_invalid(self, form):
        messages.error(self.request, "Invalid username or password.")
        return super().form_invalid(form)

    def get_success_url(self):
        user = self.request.user
        if user.is_superuser:
            return '/admin/'      # Staff redirected to admin dashboard
        if user.is_staff:
            return '/staff/'      # Staff redirected to admin dashboard
        else:
            return '/profile/'    # Regular users redirected to profile

# For public and staff users
def login_page(request):
    if request.method == "POST":
        username = request.POST.get("username")
        password = request.POST.get("password")
        
        # Try to fetch the user from DB, this is to identify if the user exists but is not active
        try:
            user_obj = User.objects.get(username=username)
        except User.DoesNotExist:
            user_obj = None

        user = authenticate(request, username=username, password=password)
        if user:
            # Login only if active
            login(request, user)
            messages.success(request, f"Welcome, {user.username}!")
            return redirect("profile")
        
        # If no user from authenticate
        if user_obj and not user_obj.is_active:
            messages.error(request, "Your account is not active. Please write an email to the <NAME_EMAIL> to re-activate your account.")
        else:
            messages.error(request, "Invalid username or password.")

    return render(request, "login.html")
    
@login_required
def logout_page(request):
    logout(request)
    messages.success(request, "You have been logged out.")
    return redirect("login")

@login_required
def profile_view(request):
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.user.is_staff:
        base_template = 'main/staff_base.html'
        loans = None
    else:
        base_template = 'main/base.html'
        loans = Loan.objects.filter(user=request.user).order_by('-borrowed_at')
        
        # Calculate fine on the fly using model method
        # Call calculate_fine for each loan
        for loan in loans:
            fine = loan.calculate_fine()   # this calls your method
            print(f"Loan ID {loan.loan_id} fine: {fine}")  # verify in console/log
            loan.fine_amount = fine        # optional, for template display
            loan.save()
            # Check if user already reviewed this book
            review = Review.objects.filter(book=loan.book, user=request.user).first()
            loan.user_review = review  # attach review object to loan for template            
            
    return render(request, 'profile.html', {
        'profile': profile,
        'user': request.user,
        'edit_mode': False,
        'base_template': base_template,
        'loans': loans
    })

@login_required
def profile_edit(request):
    if request.user.is_staff:
        base_template = 'main/staff_base.html'
    else:
        base_template = 'main/base.html'
    profile, created = UserProfile.objects.get_or_create(user=request.user)
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=profile, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, f"User profile updated successfully.")
            return redirect('profile')
    else:
        form = UserProfileForm(instance=profile, user=request.user)

    return render(request, 'profile.html', {
        'profile': profile,
        'user': request.user,
        'form': form,
        'edit_mode': True,
        'base_template':base_template
    })

@login_required
def user_dashboard_page(request):
    user = request.user
    try:
        profile = user.userprofile
    except UserProfile.DoesNotExist:
        profile = None

    search_results = None
    query = request.GET.get('query')
    if query:
        search_results = Book.objects.filter(
            title__icontains=query
        ) | Book.objects.filter(
            author__icontains=query
        ) | Book.objects.filter(
            genre__icontains=query
        )

    context = {
        'user': user,
        'profile': profile,
        'search_results': search_results
    }
    return render(request, 'user_dashboard.html', context)

def generate_verification_code():
    # Generate a random alphanumeric string
    return get_random_string(length=6, allowed_chars='ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')

def password_reset_confirm(request):
    user_id = request.session.get('password_reset_user')
    if not user_id:
        messages.error(request, "Session expired. Please request a new password reset.")
        return redirect('password_reset')

    user = User.objects.get(id=user_id)

    if request.method == "POST":
        password = request.POST.get('password')
        confirm = request.POST.get('confirm_password')
        if password != confirm:
            messages.error(request, "Passwords do not match!")
        else:
            user.set_password(password)
            user.save()
            messages.success(request, "Password reset successfully. You can now log in.")

            # Clear session data
            request.session.pop('password_reset_code', None)
            request.session.pop('password_reset_user', None)

            return redirect('login')
    return render(request, 'password_reset_confirm.html')

def password_reset_verify(request):
    if request.method == "POST":
        code_entered = request.POST.get('code')
        code_actual = request.session.get('password_reset_code')
        
        if code_entered == code_actual:
            messages.success(request, "Code verified. You can now set a new password.")
            return redirect('password_reset_confirm')
        else:
            messages.error(request, "Invalid verification code.")
            return redirect('password_reset_verify')
    
    return render(request, 'password_reset_verify.html')

@login_required
def change_password(request):
    
    if request.user.is_staff:
        base_template = 'main/staff_base.html'
    else:
        base_template = 'main/base.html'
            
    if request.method == 'POST':
        
        form = CustomPasswordChangeForm(user=request.user, data=request.POST)
        if form.is_valid():
            user = form.save()
            # update_session_auth_hash(request, user)  # Keeps user logged in
            messages.success(request, 'Your password has been updated successfully!')
            return redirect('change_password')
        else:
            messages.error(request, 'Please correct the error below.')
    else:
        form = CustomPasswordChangeForm(user=request.user)
    return render(request, 'change_password.html', {'form': form, 'base_template': base_template})

# No authentication required pages
def password_reset_page(request):
    if request.method == "POST":
        email = request.POST.get('email')
        try:
            user = User.objects.get(email=email)
            
            # Generate a random 6-digit numeric code
            code = get_random_string(length=6, allowed_chars='0123456789')
            
            # Store code and user ID in session
            request.session['password_reset_code'] = code
            request.session['password_reset_user'] = user.id
            
            send_mail(
                subject="Password Reset Verification Code",
                message=f"Hi {user.username},\nYour password reset verification code is: {code}\nIt is valid for this session.",
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
            )
            messages.success(request, "Verification code has been sent to your email.")
            return redirect('password_reset_verify')  # page to enter code
        except User.DoesNotExist:
            messages.error(request, "Email address not found.")
            return redirect('password_reset')
    return render(request, 'password_reset.html')

def signup_page(request):
    if request.method == "POST":
        username = request.POST.get("username", "").strip()
        email = request.POST.get("email", "").strip()
        password = request.POST.get("password") or ""
        confirm_password = request.POST.get("confirm_password") or ""
        first_name = request.POST.get("first_name", "").strip()
        last_name = request.POST.get("last_name", "").strip()
        bio = request.POST.get("bio", "").strip()
        gender = request.POST.get("gender", "")
        profile_picture = request.FILES.get("profile_picture")

        # Collect errors
        errors = []
        if not username or not email or not password:
            errors.append("Username, Email, and Password are required.")
        if User.objects.filter(username=username).exists():
            errors.append("Username already exists. Please choose another.")
        if User.objects.filter(email=email).exists():
            errors.append("Email already registered. Try logging in if you already have an account. Else, use a different email.")
        if password != confirm_password:
            errors.append("Passwords do not match.")
        if gender not in ("M", "F"):
            errors.append("Please select a valid gender.")

        if errors:
            for e in errors:
                messages.error(request, e)
            # IMPORTANT: pass posted values back so the template can repopulate
            return render(request, "signup.html", {"form_data": request.POST})

        # Create user
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name,
        )
        # Create profile
        UserProfile.objects.create(
            user=user,
            bio=bio,
            gender=gender,
            profile_picture=profile_picture,
        )
        # Send email (best-effort)
        try:
            send_mail(
                subject="Successful Registration on Silent Library",
                message=(
                    f"Dear {user.first_name},\n\n"
                    f"Thank you for registering on our Silent Library. We are excited to have you as a member.\n\n"
                    f"Thank you.\nSilent Library Team"
                ),
                from_email=f"Silent Library {settings.DEFAULT_FROM_EMAIL}",
                # recipient_list=[email],
                recipient_list=["<EMAIL>"],
                fail_silently=False,
            )
            # messages.success(request, "Registration successful! You can now log in.")
            return redirect("signup_success")  # or your success page
        except Exception as e:
            messages.error(request, f"Your account has been created successfully. However, there is an Error in sending email: {e}.  You still can login to the system using your credentials.")
            return redirect("login")  # or your success page

    # GET: initial load (no posted data)
    return render(request, "signup.html", {"form_data": {}})


def book_search_page(request):
    query = request.GET.get('q', '')  # Get search query from input
    books = None

    if query:
        books_list = Book.objects.filter(
            Q(title__icontains=query) |
            Q(author__author_name__icontains=query) |
            Q(genre__genre_name__icontains=query)
        )
    else:
        books_list = Book.objects.all().order_by('-book_id')
    
    if books_list:    
        paginator = Paginator(books_list, 10)  # 10 books per page
        page_number = request.GET.get('page')
        books = paginator.get_page(page_number)

    context = {
        'books': books,
        'query': query
    }
    return render(request, 'book_search.html', context)

def book_detail_page(request, book_id):
    # Select base template
    if request.user.is_staff:
        base_template = 'main/staff_base.html'
    else:
        base_template = 'main/base.html'

    book = get_object_or_404(Book, pk=book_id)

    # Check if the logged-in user has an active loan for this book
    user_loan = None
    if request.user.is_authenticated and not request.user.is_staff:
        user_loan = Loan.objects.filter(
            user=request.user,
            book=book,
            returned_at__isnull=True
        ).first()
      
        # if user has reviewed already    
        user_review = Review.objects.filter(book=book, user=request.user).first()
    if user_review:
        print(f"Review found for book {book.title}: {user_review.rating}, {user_review.comment}")

    # Get all reviews for this book 
    all_reviews = book.reviews.all().order_by('-created_at')  # newest first
    
    # Mask usernames
    for rev in all_reviews:
        if rev.user:
            username = rev.user.first_name
            if len(username) > 5:
                rev.masked_username = username[:5] + "**"
            else:
                rev.masked_username = username
        else:
            rev.masked_username = "Anonymous"

    # Pagination: 5 reviews per page
    paginator = Paginator(all_reviews, 5)
    page_number = request.GET.get('page')
    reviews_page = paginator.get_page(page_number)

    context = {
        'book': book,
        'base_template': base_template,
        'loan': user_loan,
        'review': user_review,
        'reviews_page': reviews_page
    }
    return render(request, 'book_detail.html', context)

def borrow_book(request, book_id):
    book = get_object_or_404(Book, pk=book_id)
    if book.copies_available > 0:
        # Create Loan record
        Loan.objects.create(
            user=request.user,
            book=book,
            borrowed_at=timezone.now(),
            due_date=timezone.now() + timezone.timedelta(days=14)
        )
        # Reduce available copies
        book.copies_available -= 1
        book.save()
        messages.success(request, f"Book '{book.title}' has been borrowed successfully!")
    else:
        messages.error(request, f"Book '{book.title}' is not available for borrowing.")

    return redirect("book_detail", book_id=book_id)

def browse_books(request):
    categories = Genre.objects.all()[:5]
    category_books = []

    for genre in categories:
        books = Book.objects.filter(genre=genre).select_related("author")[:6]
        books_with_actions = []

        for book in books:
            actions = {
                "book": book,
                "can_borrow": False,
                "can_return": False,
                "can_review": False,
                "due_date": None,
                "borrow_url": "",
                "return_url": "",
                "review_url": ""
            }

            if request.user.is_authenticated and not request.user.is_staff:
                loan = book.loan_set.filter(user=request.user).order_by('-borrowed_at').first()
                if loan and not loan.returned_at:
                    actions["can_return"] = True
                    actions["can_review"] = True
                    actions["due_date"] = loan.due_date
                    actions["return_url"] = ""
                    actions["review_url"] = ""
                else:
                    if book.copies_available > 0:
                        actions["can_borrow"] = True
                        actions["borrow_url"] = reverse('book_detail', args=[book.book_id])
            
            books_with_actions.append(actions)

        category_books.append({
            "genre": genre,
            "books": books_with_actions
        })

    context = {
        "category_books": category_books
    }
    return render(request, "book/browse_books.html", context)



# This is to check if the user can review a book
def can_review_book(user, book):
    """
    Check if the user can review a book.
    A user can review if they ever borrowed this book (past or present).
    """
    from .models import Loan
    return Loan.objects.filter(user=user, book=book).exists()

# Book Return
@login_required
def return_book(request, loan_id):
    loan = get_object_or_404(Loan, pk=loan_id, user=request.user)

    if loan.returned_at:
        messages.warning(request, "This book has already been returned.")
        return redirect(request.META.get('HTTP_REFERER', '/'))

    # Mark the loan as returned
    loan.returned_at = timezone.now()
    loan.save()

    # Update book availability
    book = loan.book
    book.copies_available += 1
    book.save()

    messages.success(request, f"You have successfully returned '{book.title}'.")
    # Redirect back to the page where the request came from
    return redirect(request.META.get('HTTP_REFERER', '/'))

# Create a review
@login_required
def review_page(request, book_id):
    book = get_object_or_404(Book, pk=book_id)

    if request.method == "POST":
        rating = request.POST.get("rating")
        comment = request.POST.get("comment")

        # prevent duplicate reviews by same user
        existing = Review.objects.filter(book=book, user=request.user).first()
        if existing:
            messages.error(request, "You already reviewed this book. Please edit your review instead.")
            return redirect('book_detail', book_id=book_id)

        Review.objects.create(
            book=book,
            user=request.user,
            rating=rating,
            comment=comment
        )
        messages.success(request, "Your review has been submitted.")
        return redirect(request.META.get('HTTP_REFERER', '/'))

    return render(request, 'book/review_form.html', {'book': book})

# Create your views here.
def landing_page(request):
    return render(request, "home.html")

def home_page(request):
    return render(request, "home.html")

def books_page(request):
    return render(request, "books.html")

def events_page(request):
    return render(request, "events.html")

def contact_page(request):
    return render(request, "contact.html")

def privacy_page(request):
    return render(request, "privacy.html")

def sitemap_page(request):
    return render(request, "sitemap.html")

def book_details_1_page(request):
    return render(request, "book_details_1.html")

def book_details_2_page(request):
    return render(request, "book_details_2.html")

def book_details_3_page(request):
    return render(request, "book_details_3.html")

def advanced_search_page(request):
    return render(request, "advanced_search.html")

def signup_success_page(request):
    return render(request, "signup_success.html")
